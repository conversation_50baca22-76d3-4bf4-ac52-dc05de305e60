import EventEmitter from 'eventemitter3';

const EE = new EventEmitter();

export default EE;

class EventHelper {
  // 事件类型 - 表示不同的事件类型，例如：ValueChange、ButtonClick等
  eventType: string;

  constructor(eventType) {
    this.eventType = eventType;
  }

  // mark是一个标识，用于区分不同任务的事件，例如：task1、task2
  private on(mark, callback) {
    EE.on(this.getEventName(mark), callback);
  }

  private emit(mark, ...args) {
    EE.emit(this.getEventName(mark), ...args);
  }

  private off(mark, callback) {
    EE.off(this.getEventName(mark), callback);
  }

  private getEventName(mark) {
    return `${this.eventType}_${mark}`;
  }

  // 注册事件监听器，返回一个取消监听的函数
  listen(mark, callback) {
    this.on(mark, callback);
    return () => {
      this.off(mark, callback);
    };
  }

  // 触发事件
  trigger(mark, ...args) {
    this.emit(mark, ...args);
  }
}

// 表单值变化事件
export const MessageScrollEvent = new EventHelper('MessageScrollEvent');
