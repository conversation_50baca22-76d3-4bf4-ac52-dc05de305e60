@use '../../../../sass/vars/index.scss' as *;

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 32px;
  background: $bg-color-200;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  min-height: 400px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.successContainer,
.errorContainer {
  text-align: center;
  animation: fadeInUp 0.6s ease-out;
}

.statusHeader {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 24px;
  gap: 16px;
}

.successIcon {
  font-size: 64px;
  color: $green-base;
  animation: scaleIn 0.5s ease-out 0.2s both;
}

.errorIcon {
  font-size: 64px;
  color: $red-base;
  animation: scaleIn 0.5s ease-out 0.2s both;
}

.statusTitle {
  font-size: 28px;
  font-weight: 600;
  color: $title-color;
  margin: 0;
  line-height: 1.2;
}

.statusDescription {
  font-size: 16px;
  color: $grey-color;
  margin: 0 0 32px 0;
  line-height: 1.5;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.previewContainer {
  background: $bg-color-100;
  border-radius: 12px;
  padding: 24px;
  margin-top: 32px;
  border: 1px solid $bg-color-300;
  animation: slideInUp 0.6s ease-out 0.4s both;
}

.previewHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid $bg-color-300;
}

.previewTitle {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 500;
  color: $title-color;
  flex: 1;
}

.previewIcon {
  font-size: 20px;
  color: $primary-color;
}

.openLink {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: $primary-color;
  color: $bg-color-200;
  text-decoration: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;

  &:hover {
    background: darken($primary-color, 10%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(21, 91, 212, 0.3);
    color: $bg-color-200;
    text-decoration: none;
  }

  :global(.zenticon) {
    font-size: 16px;
  }
}

.iframeWrapper {
  position: relative;
  width: 100%;
  height: 500px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid $bg-color-300;
  background: $bg-color-200;
}

.previewIframe {
  width: 100%;
  height: 100%;
  border: none;
  border-radius: 8px;
}

.actionButtons {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;
}

.retryButton {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: $primary-color;
  color: $bg-color-200;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: darken($primary-color, 10%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(21, 91, 212, 0.3);
  }

  &:active {
    transform: translateY(0);
  }

  :global(.zenticon) {
    font-size: 18px;
  }
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.5);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    margin: 0 16px;
    padding: 24px 20px;
    border-radius: 12px;
  }

  .statusTitle {
    font-size: 24px;
  }

  .statusDescription {
    font-size: 14px;
    margin-bottom: 24px;
  }

  .previewContainer {
    padding: 16px;
    margin-top: 24px;
  }

  .previewHeader {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
    text-align: center;
  }

  .previewTitle {
    justify-content: center;
    font-size: 16px;
  }

  .openLink {
    align-self: center;
    padding: 10px 20px;
  }

  .iframeWrapper {
    height: 400px;
  }

  .successIcon,
  .errorIcon {
    font-size: 48px;
  }

  .actionButtons {
    margin-top: 24px;
  }

  .retryButton {
    padding: 10px 20px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .container {
    margin: 0 8px;
    padding: 20px 16px;
  }

  .statusTitle {
    font-size: 20px;
  }

  .iframeWrapper {
    height: 300px;
  }

  .previewContainer {
    padding: 12px;
  }
}

// 加载状态样式
.loadingState {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 40px;

  .loadingIcon {
    font-size: 48px;
    color: $primary-color;
    animation: spin 1s linear infinite;
  }

  .loadingText {
    font-size: 16px;
    color: $grey-color;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
