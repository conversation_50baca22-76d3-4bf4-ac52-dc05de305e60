import React, { useState } from 'react';
import { Checkbox, Button, Input, Notify } from 'zent';
import styles from './SellPointConfirm.m.scss';
import useCustomReducer from '../../store';
import { FlowStatusEnum, FlowTemplateEnum } from 'pages/guide/constants';
import messageReducer from '../../store/message';
import { asyncGenerate } from '../../api';

interface SellPointConfirmProps {
  onSubmit?: (data: { selectedKeywords: string[]; customKeywords: string[] }) => void;
  onCancel?: () => void;
}

const SellPointConfirm: React.FC<SellPointConfirmProps> = ({ onSubmit }) => {
  const { getData, setFlowStatus, setGoods, setShowHandler } = useCustomReducer();
  const { getData: getMessageData, showMessage } = messageReducer();
  const { goodsSellPointBlock, goodsSellPointImageBlock } = getMessageData();
  const { keywords: initialKeywords = [], sellPoint } = goodsSellPointBlock?.nextData || {};
  const { images = [] } = goodsSellPointImageBlock?.nextData || {};

  const [checkedList, setCheckedList] = useState<string[]>(initialKeywords);
  const [customKeywords, setCustomKeywords] = useState<string[]>([]);
  const [newKeyword, setNewKeyword] = useState('');
  const [sellPointText, setSellPointText] = useState(sellPoint || '');
  const [checkImages, setCheckImages] = useState<string[]>(images.map(image => image.url) || []);
  const { goods } = getData();

  const { title, itemId, alias } = goods;

  const handleKeywordChange = (checkedList: string[]) => {
    setCheckedList(checkedList);
  };

  const handleAddKeyword = () => {
    if (!newKeyword.trim()) {
      Notify.error('请输入关键字');
      return;
    }

    if (initialKeywords.includes(newKeyword.trim()) || customKeywords.includes(newKeyword.trim())) {
      Notify.error('关键字已存在');
      return;
    }

    setCustomKeywords([...customKeywords, newKeyword.trim()]);
    setNewKeyword('');
    Notify.success('关键字添加成功');
  };

  const handleRemoveCustomKeyword = (keyword: string) => {
    setCustomKeywords(customKeywords.filter(k => k !== keyword));
    setCheckedList(checkedList.filter(k => k !== keyword));
  };

  const handleSubmit = () => {
    if (checkedList.length === 0) {
      Notify.error('请至少选择一个关键字');
      return;
    }
    console.log({
      selectedKeywords: checkedList,
      customKeywords,
    });

    // 执行后端接口
    asyncGenerate({
      templateId: FlowTemplateEnum.ContentGeneration,
      itemId,
      alias,
      analyze: {
        keywords: checkedList,
        sellPoint: sellPointText,
      },
      pictureAnalyze: checkImages.map(url => images.find(image => image.url === url)),
    }).then(data => {
      showMessage(FlowStatusEnum.CreateArticle, data.requestId);
    });

    setFlowStatus(FlowStatusEnum.CreateArticle);
    // showMessage(FlowStatusEnum.CreateArticle);
    setShowHandler(false);
  };

  const allKeywords = [...initialKeywords, ...customKeywords];

  return (
    <div className={styles.sellPointContainer}>
      <div className={styles.header}>
        <h1 className={styles.title}>{title}</h1>
      </div>

      <div className={styles.section}>
        <div className={styles.sectionLabel}>
          <span className={styles.icon}>💡</span>
          卖点信息
          <span className={styles.required}>*</span>
        </div>
        <div className={styles.sellPoint}>
          <Input
            className={styles.sellPointText}
            type="textarea"
            value={sellPointText}
            onChange={e => setSellPointText(e.target.value)}
          />
        </div>
      </div>

      <div className={styles.section}>
        <div className={styles.sectionLabel}>
          <span className={styles.icon}>🏷️</span>
          关键字选择
          <span className={styles.required}>*</span>
        </div>
        <div className={styles.keywordsSection}>
          <div className={styles.keywordsList}>
            <Checkbox.Group
              value={checkedList}
              onChange={handleKeywordChange}
              className={styles.keywordsGroup}
            >
              {allKeywords.map(item => (
                <div key={item} className={styles.keywordItem}>
                  <Checkbox value={item}>
                    {item}
                    {customKeywords.includes(item) && (
                      <span
                        className={styles.removeBtn}
                        onClick={e => {
                          e.stopPropagation();
                          handleRemoveCustomKeyword(item);
                        }}
                      >
                        ×
                      </span>
                    )}
                  </Checkbox>
                </div>
              ))}
            </Checkbox.Group>
          </div>

          <div className={styles.addKeywordContainer}>
            <div className={styles.addKeywordInput}>
              <Input
                value={newKeyword}
                onChange={e => setNewKeyword(e.target.value)}
                onPressEnter={handleAddKeyword}
                placeholder="输入新关键字"
              />
            </div>
            <div className={styles.addKeywordBtn}>
              <Button onClick={handleAddKeyword}>+ 添加</Button>
            </div>
          </div>

          {checkedList.length > 0 && (
            <div className={styles.selectedKeywords}>
              <div className={styles.selectedLabel}>已选择 ({checkedList.length}):</div>
              <div className={styles.selectedList}>
                {checkedList.map(keyword => (
                  <span key={keyword} className={styles.selectedTag}>
                    {keyword}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      <div>
        <div className={styles.sectionLabel}>
          <span className={styles.icon}>📷</span>
          图片选择
          <span className={styles.required}>*</span>
        </div>
        <div className={styles.imageList}>
          <Checkbox.Group value={checkImages} onChange={value => setCheckImages(value)}>
            {images.map((image, index) => (
              // 在这里渲染每一张图片
              <Checkbox value={image.url} key={index}>
                <span className={styles.imageItem}>
                  <img src={image.url} alt={`Image ${index}`} />
                  <span>{image.describe}</span>
                </span>
              </Checkbox>
            ))}
          </Checkbox.Group>
        </div>
      </div>

      <div className={styles.actionButtons}>
        <div className={styles.submitBtn}>
          <Button type="primary" onClick={handleSubmit}>
            确认提交
          </Button>
        </div>
      </div>
    </div>
  );
};

export default SellPointConfirm;
