import React from 'react';

import { BlockLoading, ding, ImageUpload, Notify } from 'zent';
import { onUpload } from '../../utils/upload';

class Simple extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
    };
  }

  onUploadChange = files => {
    if (files.length === 0) {
      this.props.onChange('');
      return;
    }
    this.setState({
      loading: true,
    });
    onUpload(files[0].file)
      .then(res => {
        this.props.onChange(res.attachmentPath);
        this.setState({
          loading: false,
        });
      })
      .catch(err => {
        this.setState({
          loading: false,
        });
      });
  };

  onUpload = (file, report) => {
    return onUpload(file).then(res => {
      return res.attachmentPath;
    });
  };

  onUploadError = (type, data) => {
    if (type === 'overMaxAmount') {
      Notify.error(`最多可上传 ${data.maxAmount} 张图片`);
    } else if (type === 'overMaxSize') {
      Notify.error(`图片大小不能超过 ${data.formattedMaxSize}`);
    }
  };

  render() {
    return (
      <BlockLoading loading={this.state.loading}>
        <ImageUpload
          className="zent-image-upload-demo"
          maxSize={5 * 1024 * 1024}
          sortable
          maxAmount={1}
          onChange={this.onUploadChange}
          onError={this.onUploadError}
        />
      </BlockLoading>
    );
  }
}

export default Simple;
