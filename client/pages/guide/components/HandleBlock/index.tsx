import React from 'react';

import './index.scss';

import GoodsSelect from './GoodsSelect';
import SellPointConfirm from './SellPointConfirm';
import ArticleCreate from './ArticleCreate';
import ArticlePublishResult from './ArticlePublishResult';

import { FlowStatusEnum } from '../../constants';
import useCustomReducer from '../../store';

const HandleBlock = () => {
  const { getData } = useCustomReducer();
  const { flowStatus } = getData();

  return (
    <div className="handle-block-wrap">
      {FlowStatusEnum.GoodsSelect === flowStatus && <GoodsSelect />}
      {FlowStatusEnum.GoodsSellPoint === flowStatus && <SellPointConfirm />}
      {FlowStatusEnum.CreateArticle === flowStatus && <ArticleCreate />}
      {FlowStatusEnum.PublishArticle === flowStatus && <ArticlePublishResult />}
    </div>
  );
};

export default HandleBlock;
