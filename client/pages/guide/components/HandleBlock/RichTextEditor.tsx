import React, { useState } from 'react';
import { Button } from 'zent';
// import { EditorState, convertToRaw, Modifier } from 'draft-js';
// import { Editor } from 'react-draft-wysiwyg';
import { onUpload } from '../../utils/upload';
import 'react-draft-wysiwyg/dist/react-draft-wysiwyg.css';

import { stateFromHTML } from 'draft-js-import-html';
import { stateToHTML } from 'draft-js-export-html';

const { EditorState, ContentState, convertToRaw, Modifier } = require('draft-js');
const { Editor } = require('react-draft-wysiwyg');

const RichTextEditor = ({ initContent, onChange }) => {
  const initialContentState = stateFromHTML(initContent);
  const initEditorState = EditorState.createWithContent(initialContentState);
  const [editorState, setEditorState] = useState(initEditorState);

  const uploadImageCallBack = async file => {
    const formData = new FormData();
    formData.append('file', file);
    console.log('file', file);

    onUpload(file).then(res => {
      console.log('onUploadres', res);
    });

    try {
      const response = await onUpload(file);
      return { data: { link: response.attachmentPath } };
    } catch (error) {
      console.error('Upload failed:', error);
      return { data: { link: null } };
    }
  };

  const onEditorStateChange = newState => {
    setEditorState(newState);
    const htmlString = stateToHTML(newState.getCurrentContent());
    onChange(htmlString);
  };

  return (
    <div style={{ height: '100%' }}>
      <Editor
        editorState={editorState}
        onEditorStateChange={onEditorStateChange}
        toolbar={{
          image: {
            uploadCallback: uploadImageCallBack,
            alt: { present: true, mandatory: false },
            inputAccept: 'image/gif,image/jpeg,image/jpg,image/png,image/svg',
          },
          options: ['inline', 'blockType', 'list', 'textAlign', 'link', 'image', 'history'],
        }}
        wrapperClassName="demo-wrapper"
        editorClassName="demo-editor"
      />
    </div>
  );
};

export default RichTextEditor;
