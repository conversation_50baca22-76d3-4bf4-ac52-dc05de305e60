import React from 'react';

import useCustomReducer from '../../store';
import { FlowStatusEnum } from '../../constants';
import messageReducer from '../../store/message';

const ArticlePublishResult = () => {
  const { setFlowStatus, setGoods, setShowHandler } = useCustomReducer();
  const { getData, showMessage } = messageReducer();

  const { publishArticleBlock } = getData();
  const { publishStatus, articleUrl } = publishArticleBlock?.nextData || {};

  return (
    <div>
      {(publishStatus && (
        <div>
          <div>发布成功</div>
          {(articleUrl && <iframe src={articleUrl} width="100%" height="100%" />) || null}
        </div>
      )) || <div>发布失败</div>}
    </div>
  );
};

export default ArticlePublishResult;
