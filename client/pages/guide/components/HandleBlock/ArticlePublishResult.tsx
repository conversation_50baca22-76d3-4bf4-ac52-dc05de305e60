import React from 'react';
import { Icon } from 'zent';

import messageReducer from '../../store/message';
import styles from './ArticlePublishResult.m.scss';

const ArticlePublishResult = () => {
  const { getData } = messageReducer();

  const { publishArticleBlock } = getData();
  const { publishStatus, articleUrl } = publishArticleBlock?.nextData || {};

  return (
    <div className={styles.container}>
      {publishStatus ? (
        <div className={styles.successContainer}>
          <div className={styles.statusHeader}>
            <Icon type="check-circle" className={styles.successIcon} />
            <h3 className={styles.statusTitle}>发布成功</h3>
          </div>
          <p className={styles.statusDescription}>您的文章已成功发布，可以通过下方预览查看效果</p>
          {articleUrl && (
            <div className={styles.previewContainer}>
              <div className={styles.previewHeader}>
                <Icon type="eye" className={styles.previewIcon} />
                <span className={styles.previewTitle}>文章预览</span>
                <a
                  href={articleUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={styles.openLink}
                >
                  <Icon type="link" />
                  在新窗口打开
                </a>
              </div>
              <div className={styles.iframeWrapper}>
                <iframe
                  src={articleUrl}
                  className={styles.previewIframe}
                  title="文章预览"
                  style={{ border: 'none' }}
                />
              </div>
            </div>
          )}
        </div>
      ) : (
        <div className={styles.errorContainer}>
          <div className={styles.statusHeader}>
            <Icon type="close-circle" className={styles.errorIcon} />
            <h3 className={styles.statusTitle}>发布失败</h3>
          </div>
          <p className={styles.statusDescription}>文章发布过程中出现了问题，请检查网络连接后重试</p>
          <div className={styles.actionButtons}>
            <button
              className={styles.retryButton}
              onClick={() => {
                // 这里可以添加重试逻辑
                console.log('重试发布');
              }}
            >
              <Icon type="refresh" />
              重试发布
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ArticlePublishResult;
