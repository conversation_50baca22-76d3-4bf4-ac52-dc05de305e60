.articleContainer {
  height: 100%;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.formField {
  display: flex;
  flex-direction: column;
  gap: 8px;

  label {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
  }
}

.titleInput {
  :global(.zent-input) {
    border-radius: 6px;
    border: 1px solid #e0e0e0;
    padding: 12px;
    font-size: 14px;
    transition: border-color 0.2s ease;

    &:focus {
      border-color: #155bd4;
      box-shadow: 0 0 0 2px rgba(21, 91, 212, 0.1);
    }
  }
}

.coverSection {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.coverPreview {
  position: relative;
  display: inline-block;
  width: 120px;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e0e0e0;
  background: #f8f8f8;

  &:hover {
    .deleteButton {
      opacity: 1;
    }
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.deleteButton {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  z-index: 999;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s ease;
  color: #fff;
  font-size: 20px;

  &:hover {
    background: rgba(0, 0, 0, 0.8);
  }

  :global(.zenticon) {
    color: white;
    font-size: 14px;
  }
}

.uploadContainer {
  width: 120px;
  height: 120px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  transition: all 0.2s ease;
  cursor: pointer;

  &:hover {
    border-color: #155bd4;
    background: #f0f6ff;
  }

  :global(.zent-image-upload-demo) {
    border: none;
    background: transparent;
    width: 100%;
    height: 100%;
  }
  :global(.zent-image-upload-item) {
    width: 120px;
    height: 120px;
  }
}

.contentEditor {
  flex: 1;
  min-height: 300px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  overflow: hidden;
}

.publishButton {
  align-self: flex-end;
  margin-top: 20px;

  :global(.zent-btn) {
    border-radius: 6px;
    height: 40px;
    padding: 0 24px;
    font-size: 16px;
    font-weight: 500;
    min-width: 120px;
  }
}

.coverTips {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

// 响应式设计
@media (max-width: 768px) {
  .articleContainer {
    padding: 16px;
    gap: 16px;
  }

  .coverPreview,
  .uploadContainer {
    width: 100px;
    height: 100px;
  }

  .publishButton {
    align-self: stretch;

    :global(.zent-btn) {
      width: 100%;
    }
  }
}
