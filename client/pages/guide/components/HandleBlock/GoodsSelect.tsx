import React, { useState } from 'react';
import { Radio, Button, Tag } from 'zent';
import './GoodsSelect.scss';
import useCustomReducer from '../../store';
import { FlowStatusEnum, FlowTemplateEnum } from '../../constants';
import messageReducer from '../../store/message';
import { asyncGenerate } from '../../api';

const GoodsSelect = () => {
  const { setFlowStatus, setGoods, setShowHandler, setRequestId } = useCustomReducer();
  const { getData, showMessage } = messageReducer();
  const { goodsSelectBlock } = getData();
  const { goodsList = [] } = goodsSelectBlock?.nextData;
  const [selectedGoodsId, setSelectedGoodsId] = useState<any>(null);

  const handleConfirm = () => {
    const goodsItem = goodsList.find(good => good.itemId === selectedGoodsId);
    // 执行后端接口
    asyncGenerate({
      templateId: FlowTemplateEnum.SellingPointAnalysisText,
      itemId: selectedGoodsId,
    }).then(data => {
      setRequestId(data.requestId);
      showMessage(FlowStatusEnum.GoodsSellPoint, data.requestId);
    });
    setGoods(goodsItem);
    setFlowStatus(FlowStatusEnum.GoodsSellPoint);
    setShowHandler(false);
  };

  return (
    <div className="goods-select-wrapper">
      <div className="goods-select-field">
        <div className="title">请选择商品</div>
      </div>
      <div className="goods-select-container">
        <div className="goods-select-list">
          <Radio.Group
            className="goods-radio-group"
            value={selectedGoodsId}
            onChange={e => setSelectedGoodsId(e.target.value)}
          >
            {goodsList.map(good => (
              <Radio key={good.itemId} value={good.itemId} className="goods-radio-item">
                <div className="goods-select-item">
                  <img
                    src={good.url}
                    width={48}
                    height={48}
                    alt={good.title}
                    className="goods-image"
                  />
                  <div className="goods-select-item-info">
                    <div className="goods-select-item-name" title={good.title}>
                      {good.title}
                    </div>
                    <div className="goods-promoteChannel">
                      {(good.promoteChannel || []).map(channel => (
                        <Tag key={channel} theme="blue" outline>
                          {channel}
                        </Tag>
                      ))}
                    </div>
                  </div>
                </div>
              </Radio>
            ))}
          </Radio.Group>
        </div>
      </div>
      <div>
        <Button
          outline
          type="primary"
          disabled={!selectedGoodsId}
          className="confirm-btn"
          onClick={handleConfirm}
        >
          确定
        </Button>
      </div>
    </div>
  );
};

export default GoodsSelect;
