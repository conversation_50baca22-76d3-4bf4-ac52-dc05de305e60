.sellPointContainer {
  max-width: 600px;
  margin: 0 auto;
  padding: 24px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.header {
  margin-bottom: 32px;

  .title {
    font-size: 24px;
    font-weight: 600;
    color: #1a1a1a;
    margin: 0 0 8px 0;
    line-height: 1.3;
  }

  .subtitle {
    font-size: 14px;
    color: #666;
    margin: 0;
  }
}

.section {
  margin-bottom: 28px;

  &:last-of-type {
    margin-bottom: 32px;
  }
}

.sectionLabel {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;

  .icon {
    margin-right: 8px;
    font-size: 18px;
  }

  .required {
    color: #ff4d4f;
    margin-left: 4px;
  }
}

.sellPointContent {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;

  .sellPointText {
  }
}

.keywordsSection {
  .keywordsGroup {
    display: flex;
    flex-direction: column;
    gap: 12px;

    // 重置 zent Checkbox.Group 的默认样式
    :global(.zent-checkbox-group) {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }
  }

  .keywordsList {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-bottom: 16px;
  }

  .keywordItem {
    // 重置 zent Checkbox 的默认样式
    :global(.zent-checkbox-wrap) {
      margin: 0;
      padding: 8px 12px;
      background: #fff;
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      transition: all 0.3s ease;
      cursor: pointer;

      &:hover {
        border-color: #40a9ff;
        background: #f6ffed;
      }
    }

    :global(.zent-checkbox-checked) {
      :global(.zent-checkbox-wrap) {
        background: #e6f7ff;
        border-color: #1890ff;
        color: #1890ff;
      }
    }

    :global(.zent-checkbox-label) {
      font-size: 14px;
      font-weight: 500;
      padding-left: 8px;
    }
  }

  .addKeywordContainer {
    display: flex;
    align-items: center;
    gap: 8px;

    .addKeywordInput {
      flex: 1;
      max-width: 200px;

      :global(.zent-input) {
        border-radius: 6px;
        border: 1px solid #d9d9d9;
        transition: all 0.3s ease;

        &:focus {
          border-color: #40a9ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
      }

      :global(.zent-input-icon) {
        color: #1890ff;
        cursor: pointer;

        &:hover {
          color: #40a9ff;
        }
      }
    }

    .addKeywordBtn {
      :global(.zent-btn) {
        border-radius: 6px;
        height: 32px;
        padding: 0 12px;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 4px;
      }
    }
  }
}

.selectedKeywords {
  margin-top: 12px;

  .selectedLabel {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
  }

  .selectedList {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .selectedTag {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    background: #e6f7ff;
    color: #1890ff;
    border: 1px solid #91d5ff;
    border-radius: 4px;
    font-size: 12px;

    .removeBtn {
      margin-left: 4px;
      cursor: pointer;
      color: #1890ff;

      &:hover {
        color: #ff4d4f;
      }
    }
  }
}

.actionButtons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;

  .submitBtn {
    :global(.zent-btn) {
      border-radius: 6px;
      height: 40px;
      padding: 0 24px;
      font-size: 16px;
      font-weight: 500;
      min-width: 100px;
    }
  }

  .cancelBtn {
    :global(.zent-btn) {
      border-radius: 6px;
      height: 40px;
      padding: 0 24px;
      font-size: 16px;
      min-width: 100px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .sellPointContainer {
    margin: 0 16px;
    padding: 20px;
  }

  .header .title {
    font-size: 20px;
  }

  .keywordsList {
    flex-direction: column;
  }

  .addKeywordContainer {
    flex-direction: column;
    align-items: stretch;

    .addKeywordInput {
      max-width: none;
    }
  }

  .actionButtons {
    flex-direction: column-reverse;

    .submitBtn,
    .cancelBtn {
      :global(.zent-btn) {
        width: 100%;
      }
    }
  }
}

// 图片列表样式
.imageList {
  display: flex;
  flex-direction: column;
  gap: 16px;

  // 重置 zent Checkbox.Group 的默认样式
  :global(.zent-checkbox-group) {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  // Checkbox 容器样式
  :global(.zent-checkbox-wrap) {
    box-sizing: border-box;
    display: flex;
    align-items: flex-start;
    width: 100%;
    padding: 16px;
    background: #fff;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      border-color: #40a9ff;
      box-shadow: 0 4px 12px rgba(64, 169, 255, 0.15);
      transform: translateY(-2px);
    }
  }

  // 选中状态样式
  :global(.zent-checkbox-checked) {
    :global(.zent-checkbox-wrap) {
      border-color: #1890ff;
      background: linear-gradient(135deg, #f6ffed 0%, #e6f7ff 100%);
      box-shadow: 0 4px 16px rgba(24, 144, 255, 0.2);
    }
  }

  // 重置单个 Checkbox 的样式
  :global(.zent-checkbox) {
    margin: 0;
  }

  // Checkbox 图标样式
  :global(.zent-checkbox-inner) {
    margin-top: 2px;
    flex-shrink: 0;
  }

  // Label 容器样式
  :global(.zent-checkbox-label) {
    flex: 1;
    margin-left: 12px;
    padding: 0;
  }
}

.imageItem {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  width: calc(100% - 32px);

  img {
    width: 180px;
    min-width: 180px;
    height: auto;
    border-radius: 8px;
    object-fit: cover;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.02);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }

  p {
    flex: 1;
    margin: 0;
    font-size: 14px;
    line-height: 1.6;
    color: #495057;
    word-break: break-word;

    // 确保文本内容能够撑开容器高度
    min-height: 80px;
    display: flex;
    align-items: center;
  }
}

// 响应式设计 - 图片列表
@media (max-width: 768px) {
  .imageList {
    gap: 12px;

    :global(.zent-checkbox-group) {
      gap: 12px;
    }

    :global(.zent-checkbox) {
      :global(.zent-checkbox-wrap) {
        padding: 12px;
      }
    }
  }

  .imageItem {
    flex-direction: column;
    gap: 12px;

    img {
      width: 100%;
      min-width: auto;
      max-width: 200px;
      align-self: center;
    }

    p {
      min-height: auto;
      text-align: center;
    }
  }
}

// 动画效果
.keywordItem {
  animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.selectedTag {
  animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
