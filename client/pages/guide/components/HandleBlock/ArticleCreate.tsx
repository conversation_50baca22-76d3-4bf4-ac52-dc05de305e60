import React, { useMemo, useState } from 'react';
import { Button, Icon, Input } from 'zent';
import RichTextEditor from './RichTextEditor';

import useCustomReducer from '../../store';
import { FlowStatusEnum, FlowTemplateEnum } from '../../constants';
import messageReducer from '../../store/message';
import ImageUpload from './ImgUpload';
import { asyncGenerate } from 'pages/guide/api';
import styles from './ArticleCreate.m.scss';

const ArticleCreate = () => {
  const { setFlowStatus, setShowHandler, getData: getBaseData } = useCustomReducer();
  const { goods } = getBaseData();
  const { getData, showMessage } = messageReducer();

  const { createArticleBlock } = getData();
  const { articleContents = {} } = createArticleBlock?.nextData || {};
  console.log('articleContents', articleContents);
  const { content, thumbMediaUrl, title } = articleContents;

  const [contentValue, setContentValue] = useState(content);
  const [thumbUrl, setThumbUrl] = useState(thumbMediaUrl || goods.url);
  const [titleValue, setTitleValue] = useState(title);

  const handlePublish = () => {
    asyncGenerate({
      templateId: FlowTemplateEnum.ContentPublication,
      contents: {
        content: contentValue,
        thumbMediaUrl: thumbUrl,
        title: titleValue,
      },
    }).then(data => {
      showMessage(FlowStatusEnum.PublishArticle, data.requestId);
    });

    setFlowStatus(FlowStatusEnum.PublishArticle);
    // showMessage(FlowStatusEnum.PublishArticle);
    setShowHandler(false);
  };

  const handleContentChange = (content: string) => {
    // 处理内容变化的逻辑
    console.log('Content changed:', content);
    setContentValue(content);
  };

  const ContentEdit = useMemo(() => {
    return <RichTextEditor initContent={content} onChange={handleContentChange} />;
  }, [content, handleContentChange]);

  const handleDeleteCover = () => {
    setThumbUrl('');
  };

  return (
    <div className={styles.articleContainer}>
      <div className={styles.formField}>
        <label>标题：</label>
        <div className={styles.titleInput}>
          <Input
            value={titleValue}
            onChange={(e: any) => setTitleValue(e.target.value)}
            placeholder="请输入文章标题"
          />
        </div>
      </div>

      <div className={styles.formField}>
        <label>封面：</label>
        <div className={styles.coverSection}>
          <div className={styles.uploadContainer}>
            {thumbUrl ? (
              <div className={styles.coverPreview}>
                <img src={thumbUrl} alt="封面" style={{ width: '120px', height: '120px' }} />
                <Icon type="remove-o" className={styles.deleteButton} onClick={handleDeleteCover} />
              </div>
            ) : (
              <ImageUpload onChange={(path: string) => setThumbUrl(path)} />
            )}
          </div>
        </div>
      </div>

      <div className={styles.formField}>
        <label>内容：</label>
        <div className={styles.contentEditor}>{ContentEdit}</div>
      </div>

      <div className={styles.publishButton}>
        <Button type="primary" onClick={handlePublish}>
          发布文章
        </Button>
      </div>
    </div>
  );
};

export default ArticleCreate;
