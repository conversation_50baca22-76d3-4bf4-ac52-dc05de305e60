import React from 'react';

import AgentBlock from '../AgentBlock';

import './index.scss';
import messageReducer from '../../store/message';
import { FlowStatusEnum, FlowTemplateEnum } from 'pages/guide/constants';
import useCustomReducer from 'pages/guide/store';
import { asyncGenerate } from 'pages/guide/api';

let loadedImage = false;

const MessageBlock = ({ block, messageId, type }) => {
  console.log('block', block);
  const { getData } = useCustomReducer();
  const { requestId, goods } = getData();
  const { getData: getMessageData, handleMessageFinished, showMessage } = messageReducer();

  const handleComplete = () => {
    const { goodsSelectBlock, goodsSellPointBlock } = getMessageData();
    console.log('goodsSelectBlock', goodsSelectBlock);
    handleMessageFinished(type);
    // 如果是商品信息完成，展示商品卖点图片流程
    if (
      FlowStatusEnum.GoodsSellPoint === type &&
      goodsSellPointBlock?.fetchFinished &&
      !loadedImage
    ) {
      loadedImage = true;
      // 执行后端接口
      asyncGenerate({
        templateId: FlowTemplateEnum.SellingPointAnalysisImage,
        itemId: goods.itemId,
      }).then(data => {
        showMessage(FlowStatusEnum.GoodsSellPointImages, data.requestId);
      });
    }
  };
  return <AgentBlock block={block} messageId={messageId} onComplete={handleComplete} />;
};

export default MessageBlock;
