import React, { useMemo } from 'react';
import TextMessageBlock from './components/TextMessageBlock';
import ImageMessageBlock from './components/ImageMessageBlock';
import ThoughtChainMessageBlock from './components/ThoughtChainMessageBlock';
import TaskExecutionMessageBlock from './components/TaskExecutionMessageBlock';

export enum AgentMessageBlockTypeEnum {
  TEXT = 0,
  IMAGE = 1,
  BTN_GROUP = 2,
  THOUGHT_CHAIN = 3,
  TASK_EXECUTION = 4,
}

// 使用 React.memo 包装各个子组件，避免不必要的重新渲染
const MemoizedTextMessageBlock = React.memo(TextMessageBlock);
const MemoizedImageMessageBlock = React.memo(ImageMessageBlock);
const MemoizedThoughtChainMessageBlock = React.memo(ThoughtChainMessageBlock);
const MemoizedTaskExecutionMessageBlock = React.memo(TaskExecutionMessageBlock);

// 对比 ThoughtChain 内容是否相同
const isThoughtChainEqual = (prevBlock: any, nextBlock: any) => {
  const prevThought = prevBlock.thoughtChain;
  const nextThought = nextBlock.thoughtChain;

  // 如果步骤数量不同，需要重新渲染
  if (prevThought.steps?.length !== nextThought.steps?.length) {
    return false;
  }

  // 如果当前步骤不同，需要重新渲染
  if (prevThought.currentStep !== nextThought.currentStep) {
    return false;
  }

  // 检查最后一步的内容是否变化（通常是流式更新的地方）
  const prevLastStep = prevThought.steps?.[prevThought.steps.length - 1];
  const nextLastStep = nextThought.steps?.[nextThought.steps.length - 1];

  if (prevLastStep?.content !== nextLastStep?.content) {
    return false;
  }

  return true;
};

// 对 AgentBlock 组件使用 React.memo 并添加自定义比较函数
const AgentBlock = React.memo(
  ({ block, messageId, onComplete }: any) => {
    // 生成唯一的 messageId
    const uniqueMessageId = useMemo(() => {
      // 优先使用传入的 messageId，其次使用 block.id（如果存在），否则生成随机 ID
      return (
        messageId ||
        (block as any).id ||
        `msg_${Date.now()}_${Math.random()
          .toString(36)
          .substr(2, 9)}`
      );
    }, [messageId, block]);

    // 使用 useMemo 缓存渲染结果
    const renderedBlock = useMemo(() => {
      const { type } = block;
      switch (type) {
        case AgentMessageBlockTypeEnum.TEXT:
          return <MemoizedTextMessageBlock {...block} messageId={uniqueMessageId} />;
        case AgentMessageBlockTypeEnum.IMAGE:
          return <MemoizedImageMessageBlock {...block} />;
        case AgentMessageBlockTypeEnum.THOUGHT_CHAIN:
          return (
            <MemoizedThoughtChainMessageBlock
              {...block}
              messageId={uniqueMessageId}
              onComplete={onComplete}
            />
          );
        case AgentMessageBlockTypeEnum.TASK_EXECUTION:
          return <MemoizedTaskExecutionMessageBlock {...block} />;
        default:
          return null;
      }
    }, [block, uniqueMessageId]);

    return renderedBlock;
  },
  (prevProps, nextProps) => {
    // 自定义比较函数，只有真正需要更新的情况才返回 false（允许重新渲染）

    // 如果 messageId 变化了，需要重新渲染
    if (prevProps.messageId !== nextProps.messageId) {
      return false;
    }

    // 如果 block 引用变了，但内容相同，不需要重新渲染
    const prevBlock = prevProps.block;
    const nextBlock = nextProps.block;

    // 检查类型是否相同
    if (prevBlock.type !== nextBlock.type) {
      return false;
    }

    // 根据不同类型的 block 进行深度比较
    switch (nextBlock.type) {
      case AgentMessageBlockTypeEnum.TEXT:
        // 对于文本消息，只比较 markdown 内容
        return (prevBlock as any).markdown === (nextBlock as any).markdown;
      case AgentMessageBlockTypeEnum.THOUGHT_CHAIN:
        // 思考链比较复杂，需要检查当前步骤和内容
        return isThoughtChainEqual(prevBlock, nextBlock);

      // 其他类型的消息可以根据需要添加更多比较逻辑
      default:
        // 默认情况下，如果引用不同就重新渲染
        return false;
    }
  },
);

export default AgentBlock;
