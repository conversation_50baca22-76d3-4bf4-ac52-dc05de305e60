import React, { useState, useEffect, useRef } from 'react';
import styles from './style.m.scss';
import MarkdownText from '../MarkdownText';

// 使用全局状态映射来跟踪每个消息ID的打字进度
const globalTypingState: Record<
  string,
  {
    displayedText: string;
    isFinished: boolean;
    index: number;
    lastText: string;
  }
> = {};

interface TypewriterEffectProps {
  text: string;
  typingSpeed?: number;
  onComplete?: () => void;
  isComplete?: boolean;
  messageId?: string; // 添加消息ID参数
}

// 创建一个简单的打字效果组件
const TypewriterEffect = ({
  text,
  typingSpeed = 30,
  onComplete,
  isComplete = false,
  messageId = 'default', // 默认ID
  fetchFinished,
}: any) => {
  // 使用消息ID初始化组件状态
  const [displayedText, setDisplayedText] = useState(() => {
    return globalTypingState[messageId]?.displayedText || '';
  });

  const [isFinished, setIsFinished] = useState(() => {
    return globalTypingState[messageId]?.isFinished || false;
  });

  const lastTextRef = useRef(globalTypingState[messageId]?.lastText || '');
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const indexRef = useRef(globalTypingState[messageId]?.index || 0);

  // 更新全局状态的辅助函数
  const updateGlobalState = (
    displayedText: string,
    isFinished: boolean,
    index: number,
    lastText: string,
  ) => {
    globalTypingState[messageId] = {
      displayedText,
      isFinished,
      index,
      lastText,
    };
  };

  useEffect(() => {
    if (isComplete) {
      setDisplayedText(text);
      setIsFinished(true);
      updateGlobalState(text, true, text.length, text);
      onComplete?.();
      return;
    }

    // 如果当前文本包含之前的文本，只需要继续打字
    if (text.startsWith(lastTextRef.current)) {
      const startTypingFromIndex = displayedText.length;

      // 清除之前可能存在的timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // 重置完成状态
      setIsFinished(false);

      // 继续从上次停止的地方开始打字
      const continueTyping = () => {
        if (indexRef.current < text.length) {
          const newDisplayedText = text.substring(0, indexRef.current + 1);
          setDisplayedText(newDisplayedText);
          updateGlobalState(newDisplayedText, false, indexRef.current + 1, lastTextRef.current);
          indexRef.current++;
          timeoutRef.current = setTimeout(continueTyping, typingSpeed);
        } else {
          setIsFinished(true);
          updateGlobalState(text, true, indexRef.current, text);
          onComplete?.();
        }
      };

      // 如果之前已经打完了所有字符，直接从新内容开始
      if (lastTextRef.current === displayedText) {
        indexRef.current = startTypingFromIndex;
      }

      // 启动打字
      continueTyping();
    } else {
      // 如果文本完全变了，重新开始打字
      setIsFinished(false);
      indexRef.current = 0;

      // 清除之前可能存在的timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      const typeNextChar = () => {
        if (indexRef.current < text.length) {
          const newDisplayedText = text.substring(0, indexRef.current + 1);
          setDisplayedText(newDisplayedText);
          updateGlobalState(newDisplayedText, false, indexRef.current + 1, text);
          indexRef.current++;
          timeoutRef.current = setTimeout(typeNextChar, typingSpeed);
        } else {
          setIsFinished(true);
          updateGlobalState(text, true, indexRef.current, text);
          onComplete?.();
        }
      };

      typeNextChar();
    }

    // 更新最后一次的文本引用
    lastTextRef.current = text;

    return () => {
      // 清理timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [text, typingSpeed, onComplete, isComplete, messageId]);

  // 当打字结束后，使用标准的MarkdownText组件渲染
  if (isComplete && isFinished) {
    return <MarkdownText>{text}</MarkdownText>;
  }

  // 在打字过程中，使用一个简单的预格式化文本元素
  const textToShow = isFinished ? text : displayedText;

  return (
    <>
      <MarkdownText className={styles.typewriterText}>{textToShow}</MarkdownText>
      {(!isFinished || !fetchFinished) && <span className={styles.cursor}></span>}
    </>
  );
};

export default TypewriterEffect;
