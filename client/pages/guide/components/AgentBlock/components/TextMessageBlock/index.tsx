import React from 'react';
import { AgentTextMessageBlock } from '@youzan-cloud/cloud-biz-types';
import MarkdownText from '../MarkdownText';
import TypewriterEffect from '../TypewriterEffect';
import styles from './style.m.scss';

type TextMessageBlockProps = Omit<AgentTextMessageBlock, 'type'> & {
  isStreamTyping?: boolean;
  messageId?: string;
};

const TextMessageBlock = ({
  markdown,
  isStreamTyping = false,
  messageId,
}: TextMessageBlockProps) => {
  return isStreamTyping ? (
    <div className={styles.textMessageBlock}>
      <TypewriterEffect text={markdown} messageId={messageId} fetchFinished />
    </div>
  ) : (
    <MarkdownText className={styles.textMessageBlock}>{markdown}</MarkdownText>
  );
};

export default TextMessageBlock;
