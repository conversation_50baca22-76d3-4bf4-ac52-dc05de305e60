import { atom, useAtom } from 'jotai';
import { FlowStatusEnum } from '../constants';
import { messageConfig } from './messageConfig';
import {
  fetchCreateArticleResult,
  fetchGoodsSelectResult,
  fetchGoodsSellPointImagesResult,
  fetchGoodsSellPointResult,
  fetchPublishArticleResult,
} from './messageLoopHelper';
import { showHandlerAtom, requestIdAtom } from './index';

export const goodsSelectMessageAtom = atom(messageConfig[FlowStatusEnum.GoodsSelect]);
export const goodsSellPointMessageAtom = atom(messageConfig[FlowStatusEnum.GoodsSellPoint]);
export const goodsSellPointImageMessageAtom = atom(
  messageConfig[FlowStatusEnum.GoodsSellPointImages],
);
export const createArticleMessageAtom = atom(messageConfig[FlowStatusEnum.CreateArticle]);
export const publishArticleMessageAtom = atom(messageConfig[FlowStatusEnum.PublishArticle]);

function transformMessage(messageConfig) {
  console.log('messageConfigmessageConfig', messageConfig);
  const {
    id,
    show,
    finish,
    fetchFinished,
    processConfig,
    finishConfig,
    type,
    title,
    nextData,
    agentType,
  } = messageConfig;
  if (!show) {
    return null;
  }
  const thinkingBlock = {
    id,
    type: 3, // 思维链
    flowType: type,
    isStreamTyping: true,
    fetchFinished,
    thoughtChain: {
      isAutoCollapse: true,
      title: !finish ? `${title}思考中...` : `${title}完成思考`,
      currentStep: 0,
      steps: [
        {
          topic: processConfig.topic,
          content: processConfig.content,
        },
      ],
    },
    nextData,
    processConfig,
  };
  if (typeof agentType === 'number') {
    thinkingBlock.type = agentType;
    // @ts-ignore
    thinkingBlock.markdown = processConfig.content;
  }
  if (finish) {
    thinkingBlock.thoughtChain.steps.push(finishConfig);
  }
  return thinkingBlock;
}

export const goodsSelectBlockAtom = atom(get => {
  return transformMessage(get(goodsSelectMessageAtom));
});

export const goodsSellPointBlockAtom = atom(get => {
  return transformMessage(get(goodsSellPointMessageAtom));
});
export const goodsSellPointImageBlockAtom = atom(get => {
  return transformMessage(get(goodsSellPointImageMessageAtom));
});

export const createArticleBlockAtom = atom(get => {
  return transformMessage(get(createArticleMessageAtom));
});

export const publishArticleBlockAtom = atom(get => {
  return transformMessage(get(publishArticleMessageAtom));
});

export const currentRequestIdAtom = atom(get => {
  return get(requestIdAtom);
});

export default function messageReducer() {
  const [goodsSelectMessage, setGoodsSelectMessage] = useAtom(goodsSelectMessageAtom);
  const [goodsSellPointMessage, setGoodsSellPointMessage] = useAtom(goodsSellPointMessageAtom);
  const [goodsSellPointImageMessage, setGoodsSellPointImageMessage] = useAtom(
    goodsSellPointImageMessageAtom,
  );
  const [createArticleMessage, setCreateArticleMessage] = useAtom(createArticleMessageAtom);
  const [publishArticleMessage, setPublishArticleMessage] = useAtom(publishArticleMessageAtom);
  const [goodsSelectBlock] = useAtom(goodsSelectBlockAtom);
  const [goodsSellPointBlock] = useAtom(goodsSellPointBlockAtom);
  const [goodsSellPointImageBlock] = useAtom(goodsSellPointImageBlockAtom);
  const [createArticleBlock] = useAtom(createArticleBlockAtom);
  const [publishArticleBlock] = useAtom(publishArticleBlockAtom);

  const [_, setShowHandler] = useAtom(showHandlerAtom);
  const [requestId] = useAtom(currentRequestIdAtom);

  const getData = () => {
    return {
      goodsSelectMessage,
      goodsSellPointMessage,
      createArticleMessage,
      publishArticleMessage,
      goodsSelectBlock,
      goodsSellPointBlock,
      goodsSellPointImageBlock,
      createArticleBlock,
      publishArticleBlock,
    };
  };

  //  设置流程消息
  // 类型， 返回数据， 是否请求完成
  const setProcessMessage = (type, resData, fetchFinished) => {
    console.log('setProcessMessage', type, resData, fetchFinished);
    const { content } = resData;
    if (!content) {
      return;
    }
    switch (type) {
      case FlowStatusEnum.GoodsSelect: {
        // 如果消息完成并且有数据，设置nextData
        const nextData: any = {};
        if (fetchFinished && resData.goodsList) {
          nextData.goodsList = resData.goodsList;
        }
        setGoodsSelectMessage({
          ...goodsSelectMessage,
          show: true,
          fetchFinished,
          processConfig: { ...goodsSelectMessage.processConfig, content },
          nextData,
        });
        break;
      }
      case FlowStatusEnum.GoodsSellPoint: {
        // 如果消息完成并且有数据，设置nextData
        const nextData: any = {};
        if (fetchFinished && resData.keywords) {
          nextData.keywords = resData.keywords;
          nextData.sellPoint = resData.sellPoint;
        }
        setGoodsSellPointMessage({
          ...goodsSellPointMessage,
          show: true,
          fetchFinished,
          processConfig: { ...goodsSellPointMessage.processConfig, content },
          nextData,
        });
        break;
      }
      case FlowStatusEnum.GoodsSellPointImages: {
        // 如果消息完成并且有数据，设置nextData
        const nextData: any = {};
        if (fetchFinished && resData.images) {
          nextData.images = resData.images;
        }
        setGoodsSellPointImageMessage({
          ...goodsSellPointImageMessage,
          show: true,
          fetchFinished,
          processConfig: { ...goodsSellPointImageMessage.processConfig, content },
          nextData,
        });
        break;
      }
      case FlowStatusEnum.CreateArticle: {
        // 如果消息完成并且有数据，设置nextData
        const nextData: any = {};
        if (fetchFinished && resData.articleContents) {
          nextData.articleContents = resData.articleContents;
        }
        setCreateArticleMessage({
          ...createArticleMessage,
          show: true,
          fetchFinished,
          processConfig: { ...createArticleMessage.processConfig, content },
          nextData,
        });
        break;
      }
      case FlowStatusEnum.PublishArticle: {
        // 如果消息完成并且有数据，设置nextData
        const nextData: any = {};
        if (fetchFinished) {
          nextData.articleUrl = resData.articleUrl;
          nextData.publishStatus = resData.publishStatus;
        }
        setPublishArticleMessage({
          ...publishArticleMessage,
          show: true,
          fetchFinished,
          processConfig: { ...publishArticleMessage.processConfig, content },
          nextData,
        });
        break;
      }
      default:
        break;
    }
  };

  const startFetchGoodsSelectResult = opt => {
    fetchGoodsSelectResult(opt, res => {
      setProcessMessage(FlowStatusEnum.GoodsSelect, res, res.finished);
    });
  };

  const startFetchGoodsSellPointResul = opt => {
    fetchGoodsSellPointResult(opt, res => {
      setProcessMessage(FlowStatusEnum.GoodsSellPoint, res, res.finished);
    });
  };

  const startFetchGoodsSellPointImagesResul = opt => {
    fetchGoodsSellPointImagesResult(opt, res => {
      setProcessMessage(FlowStatusEnum.GoodsSellPointImages, res, res.finished);
    });
  };

  const startFetchCreateArticleResult = opt => {
    fetchCreateArticleResult(opt, res => {
      setProcessMessage(FlowStatusEnum.CreateArticle, res, res.finished);
    });
  };

  const startFetchPublishArticleResult = opt => {
    fetchPublishArticleResult(opt, res => {
      setProcessMessage(FlowStatusEnum.PublishArticle, res, res.finished);
    });
  };

  // 显示消息
  const showMessage = (type, requestId) => {
    switch (type) {
      case FlowStatusEnum.GoodsSelect: {
        setGoodsSelectMessage({
          ...goodsSelectMessage,
          show: true,
        });
        startFetchGoodsSelectResult({ requestId });
        break;
      }

      case FlowStatusEnum.GoodsSellPoint: {
        setGoodsSellPointMessage({
          ...goodsSellPointMessage,
          show: true,
        });
        startFetchGoodsSellPointResul({ requestId });
        break;
      }

      case FlowStatusEnum.GoodsSellPointImages: {
        setGoodsSellPointImageMessage({
          ...goodsSellPointImageMessage,
          show: true,
        });
        startFetchGoodsSellPointImagesResul({ requestId });
        break;
      }
      case FlowStatusEnum.CreateArticle: {
        setCreateArticleMessage({
          ...createArticleMessage,
          show: true,
        });
        startFetchCreateArticleResult({ requestId });
        break;
      }
      case FlowStatusEnum.PublishArticle: {
        setPublishArticleMessage({
          ...publishArticleMessage,
          show: true,
        });
        startFetchPublishArticleResult({ requestId });
        break;
      }

      default:
        break;
    }
  };

  // 处理消息完成
  const handleMessageFinished = type => {
    console.log('handleMessageFinished[type, time]: ', type, Date.now());
    console.log('goodsSelectBlock?.fetchFinished', goodsSelectBlock);
    switch (type) {
      case FlowStatusEnum.GoodsSelect:
        if (goodsSelectBlock?.fetchFinished) {
          setGoodsSelectMessage({
            ...goodsSelectMessage,
            finish: true,
            fetchFinished: true,
            nextData: goodsSelectBlock.nextData,
            processConfig: goodsSelectBlock.processConfig,
          });
          setShowHandler(true);
        }
        break;
      case FlowStatusEnum.GoodsSellPoint:
        if (goodsSellPointBlock?.fetchFinished) {
          setGoodsSellPointMessage({
            ...goodsSellPointMessage,
            finish: true,
            fetchFinished: true,
            nextData: goodsSellPointBlock.nextData,
            processConfig: goodsSellPointBlock.processConfig,
          });
        }
        break;
      case FlowStatusEnum.GoodsSellPointImages:
        if (goodsSellPointImageBlock?.fetchFinished) {
          setGoodsSellPointImageMessage({
            ...goodsSellPointImageMessage,
            finish: true,
            fetchFinished: true,
            nextData: goodsSellPointImageBlock.nextData,
            processConfig: goodsSellPointImageBlock.processConfig,
          });
          setShowHandler(true);
        }
        break;
      case FlowStatusEnum.CreateArticle:
        if (createArticleBlock?.fetchFinished) {
          setCreateArticleMessage({
            ...createArticleMessage,
            finish: true,
            fetchFinished: true,
            nextData: createArticleBlock.nextData,
            processConfig: createArticleBlock.processConfig,
          });
          setShowHandler(true);
        }
        break;
      case FlowStatusEnum.PublishArticle:
        if (publishArticleBlock?.fetchFinished) {
          setPublishArticleMessage({
            ...publishArticleMessage,
            finish: true,
            fetchFinished: true,
            nextData: publishArticleMessage.nextData,
            processConfig: publishArticleBlock.processConfig,
          });
          setShowHandler(true);
        }
        break;
      default:
        break;
    }
  };

  // 默认初始化商品选择消息
  function initGoodsSelectMessage({ title }) {
    setGoodsSelectMessage({
      ...goodsSelectMessage,
      show: true,
      processConfig: {
        ...goodsSelectMessage.processConfig,
        content: `你已选择商品“${title}”。`,
      },
      fetchFinished: true,
      // agent类型
      // @ts-ignore
      agentType: 0,
    });
  }

  return {
    getData,
    showMessage,
    startFetchGoodsSelectResult,
    handleMessageFinished,
    setGoodsSellPointMessage,
    setCreateArticleMessage,
    setPublishArticleMessage,
    initGoodsSelectMessage,
  };
}
