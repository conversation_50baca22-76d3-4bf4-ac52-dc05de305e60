import { atom, useAtom } from 'jotai';
import { FlowStatusEnum } from '../constants';

export const showHandlerAtom = atom<boolean>(false);
export const flowStatusAtom = atom<FlowStatusEnum | string>(FlowStatusEnum.GoodsSelect);
export const goodsAtom = atom<any>({});
export const requestIdAtom = atom<string>('');

export const isStartAtom = atom(false);

export default function useCustomReducer() {
  const [isStart, setIsStart] = useAtom(isStartAtom);
  const [showHandler, setShowHandler] = useAtom(showHandlerAtom);
  const [flowStatus, setFlowStatus] = useAtom(flowStatusAtom);
  // 选中的商品
  const [goods, setGoods] = useAtom(goodsAtom);
  const [requestId, setRequestId] = useAtom(requestIdAtom);

  const getData = () => {
    return {
      showHandler,
      flowStatus,
      goods,
      isStart,
      requestId,
    };
  };

  return { getData, setIsStart, setShowHandler, setFlowStatus, setGoods, setRequestId };
}
