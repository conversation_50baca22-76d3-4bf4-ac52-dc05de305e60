import { FlowStatusEnum } from '../constants';

export const messageConfig = {
  [FlowStatusEnum.GoodsSelect]: {
    id: 0,
    type: FlowStatusEnum.GoodsSelect,
    show: false,
    title: '商品获取',
    // 是否请求结束
    fetchFinished: false,
    finish: false,
    // 思考中的内容
    processConfig: {
      topic: '根据商品选择获取数据',
      content: '',
    },
    // 完成思考的结论
    finishConfig: {
      topic: '商品获取完成',
      content: '请从右侧选择你想要生成文章的商品',
    },
    // 下一步骤的数据
    nextData: {},
  },
  [FlowStatusEnum.GoodsSellPoint]: {
    id: 1,
    type: FlowStatusEnum.GoodsSellPoint,
    show: false,
    title: '商品卖点',
    // 是否请求结束
    fetchFinished: false,
    finish: false,
    // 思考中的内容
    processConfig: {
      topic: '获取商品的卖点文本信息',
      content: '',
    },
    // 完成思考的结论
    finishConfig: {
      topic: '商品卖点文本信息获取完成',
      content: '接下来获取商品卖点图片',
    },
    // 下一步骤的数据
    nextData: {},
  },
  [FlowStatusEnum.GoodsSellPointImages]: {
    id: 2,
    type: FlowStatusEnum.GoodsSellPointImages,
    show: false,
    title: '商品卖点图片',
    // 是否请求结束
    fetchFinished: false,
    finish: false,
    // 思考中的内容
    processConfig: {
      topic: '获取商品的卖点图片',
      content: '',
    },
    // 完成思考的结论
    finishConfig: {
      topic: '商品卖点图片获取完成',
      content: '请从右侧选择你想要生成文章的商品的图片',
    },
    // 下一步骤的数据
    nextData: {},
  },
  [FlowStatusEnum.CreateArticle]: {
    id: 3,
    type: FlowStatusEnum.CreateArticle,
    show: false,
    title: '文章生成',
    // 是否请求结束
    fetchFinished: false,
    finish: false,
    // 思考中的内容
    processConfig: {
      topic: '根据商品卖点生成文章',
      content: '',
    },
    // 完成思考的结论
    finishConfig: {
      topic: '文章生成完成',
      content: '请查看右测文章内容并修改后进行发布',
    },
    // 下一步骤的数据
    nextData: {},
  },
  [FlowStatusEnum.PublishArticle]: {
    id: 4,
    type: FlowStatusEnum.PublishArticle,
    // 是否请求结束
    fetchFinished: false,
    show: false,
    title: '文章发布',
    finish: false,
    // 思考中的内容
    processConfig: {
      topic: '根据内容发布文章',
      content: '',
    },
    // 完成思考的结论
    finishConfig: {
      topic: '文章发布完成',
      content: '公众号推文已完成！',
    },
    // 下一步骤的数据
    nextData: {},
  },
};
