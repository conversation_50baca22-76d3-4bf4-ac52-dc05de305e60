import { getGenerateResult } from '../api';
import { FlowStatusEnum, FlowTemplateEnum } from '../constants';

const mockGoodsList = [
  {
    alias: '1yhj8efkuin1j',
    itemId: 380132328,
    title: '丽晶大床房测试销售方案',
    url: 'https://img01.yzcdn.cn/upload_files/2020/02/18/FmK5Q3rznRKAXJjfZpk-1a1tbmUJ.png',
  },
];

const mockGoodsSellPoint = {
  keywords: ['奢华大床房', '五星级床品', '静音睡眠环境', '全景落地窗', '智能温控系统'],
  sellPoint:
    '丽晶大床房提供奢华大床、五星级床品，配备高密度床垫（400TC精梳棉）和静音设计（隔音分贝35dB），结合全景落地窗带来的视觉享受和智能温控系统（±0.5℃精度），为商务及休闲旅客打造无干扰的五星级睡眠体验，满足对舒适与品质的双重追求。',
};

const mockImages = [
  {
    url: 'https://img01.yzcdn.cn/upload_files/2020/02/18/FmK5Q3rznRKAXJjfZpk-1a1tbmUJ.png',
    describe:
      '图片展示了一座风景优美的热带小岛，沙滩洁白，海水清澈见底，蓝天白云，岸边椰林成荫，水面上漂浮着一只独木舟，整体氛围极具度假感与放松感。该场景非常适合用于宣传旅游度假、海岛游、度假酒店、休闲用品、户外水上运动装备等相关商品，能够强烈吸引渴望休闲、远离都市、亲近自然的消费人群。图片色彩明亮，氛围轻松，具备极高的视觉吸引力和代入感，是理想的品牌宣传素材。',
    score: 0,
  },
  {
    url: 'https://img01.yzcdn.cn/upload_files/2020/02/18/FmK5Q3rznRKAXJjfZpk-1a1tbmUJ.png',
    describe:
      '图片展示了一座风景优美的热带小岛，沙滩洁白，海水清澈见底，蓝天白云，岸边椰林成荫，水面上漂浮着一只独木舟，整体氛围极具度假感与放松感。该场景非常适合用于宣传旅游度假、海岛游、度假酒店、休闲用品、户外水上运动装备等相关商品，能够强烈吸引渴望休闲、远离都市、亲近自然的消费人群。图片色彩明亮，氛围轻松，具备极高的视觉吸引力和代入感，是理想的品牌宣传素材。',
    score: 0,
  },
];

const mockArticleHtml = `
<div class="ai-index-no-animation-icon
            entry-min-height_11wt5_101
            
            markdown-typing-all
            ">
            
            <!--636-->
            
            <!--637-->
            
            <div class="ai-entry-box_11wt5_195">
                <div class="ai-entry ai-entry_11wt5_24
                        
                        " id=""><div><!--697--><div><div data-show-ext=""><div class="
            thinking-steps_1nxml_1
            
            fold_1nxml_20
        " disable-copy="" id="0" style="clear: both;">
        <header data-show-area="" data-show-ext="{&quot;value&quot;:&quot;answer_title&quot;,&quot;enter_type&quot;:&quot;ai_explore_home&quot;,&quot;valueInfo&quot;:[{&quot;component_name&quot;:&quot;thinkingSteps&quot;}],&quot;ask_model&quot;:&quot;DeepSeek-R1&quot;,&quot;satisfy_model&quot;:&quot;DeepSeek-V3-0324&quot;}" data-observered="true" class="animation-stop_hvvg3_49">
        <div class="title-container_hvvg3_7">
            <img src="https://gips3.baidu.com/it/u=3993729449,4068610368&amp;fm=3028&amp;app=3028&amp;f=PNG&amp;fmt=auto&amp;q=100&amp;size=f48_48"><!--705-->
            <div class="title_hvvg3_7">DeepSeek-R1 回答完成</div>
        </div>
        <!--706-->
        <!--707-->
    </header>
        <!--708-->
    </div><!--702--><!--699--></div><div data-show-ext=""><div class="cosd-markdown cos-space-mt-lg" id="1"><!--712--><div class="cosd-markdown-content cosd-markdown-content-typingall" style="height: auto;"><div class="marklang">
                    <p class="marklang-paragraph">以下是实现异步目标函数轮询的JavaScript代码：</p>
                </div></div></div><!--709--></div><div data-show-ext=""><div class="workspace_lqcr9_1 cos-space-mt-lg" id="2"><div><!--720--><div data-show-area="" need-click-log="" data-show-ext="{&quot;value&quot;:&quot;produce_card&quot;,&quot;query&quot;:[{&quot;type&quot;:&quot;TEXT&quot;,&quot;data&quot;:{&quot;text&quot;:{&quot;query&quot;:&quot;帮我用js写一个loop函数，每500ms执行一次目标函数，当目标函数返回了finished: true，终止循环，目标函数为异步&quot;,&quot;text_type&quot;:&quot;&quot;}}}],&quot;status&quot;:&quot;finish&quot;,&quot;card_name&quot;:&quot;code&quot;}" data-click-log="{&quot;value&quot;:&quot;produce_card&quot;,&quot;ubcExt&quot;:{&quot;query&quot;:[{&quot;type&quot;:&quot;TEXT&quot;,&quot;data&quot;:{&quot;text&quot;:{&quot;query&quot;:&quot;帮我用js写一个loop函数，每500ms执行一次目标函数，当目标函数返回了finished: true，终止循环，目标函数为异步&quot;,&quot;text_type&quot;:&quot;&quot;}}}],&quot;status&quot;:&quot;finish&quot;,&quot;card_name&quot;:&quot;code&quot;}}" class="content_lqcr9_5" data-observered="true"><img class="leftImg_lqcr9_17" src="https://psstatic.cdn.bcebos.com/basics/aichat/code-wise_1747313551000.png"><div class="rightContent_lqcr9_22"><div class="rightContentTitle_lqcr9_29">asyncLoop.js</div><div class="rightContentContent_lqcr9_38">已完成</div></div></div><!--720--><!--719--></div><!--721--></div><!--716--></div><div data-show-ext=""><div class="cosd-markdown cos-space-mt-lg" id="3"><!--751--><div class="cosd-markdown-content cosd-markdown-content-typingall" style="height: auto;"><div class="marklang">
                    <p class="marklang-paragraph">这个函数会每500ms执行一次异步目标函数，当目标函数返回对象包含finished<true></true>时停止循环，并处理可能的错误。示例展示了如何使用这个异步轮询函数。</p>
                </div></div></div><!--748--></div><!--698--><!--703--></div><!--697--><!--640--></div><!--639--></div>
            </div>
        </div>
`;

const articleUrl = 'https://zhuanlan.zhihu.com/p/89719214';

function fetchGoodsSelectApi(count) {
  const mockContent =
    '我是一名智能数据助手，致力于为老板提供专业、精准的数据报表和深度分析。为了更好地完成这一任务，我深入学习了店铺的行业动态、品类特点、经营状况以及客户群体等知识。这些知识为我呈现数据报表提供了坚实基础。同时，我重点关注老板最为关心的业绩完成情况，通过数据分析帮助老板洞察店铺的运营状况，为决策提供有力支持。';
  return new Promise(resolve => {
    const content = mockContent.slice(0, 75 * count);
    setTimeout(() => {
      resolve({
        content,
        finished: content === mockContent,
        goodsList: mockGoodsList,
        images: mockImages,
        // 卖点信息mock
        ...mockGoodsSellPoint,
        // 文章创建mock
        articleContents: {
          content: mockArticleHtml,
          title: '文章标题',
          thumbMediaUrl:
            'https://img01.yzcdn.cn/upload_files/2025/05/29/FlIIxFXZb9cEV4FQ6XaAOahiFhbt.jpg!small.webp',
        },
        // 文章链接mock
        articleUrl,
      });
    }, 300);
  });
}

function fetchGenerateResult({ templateId, requestId }) {
  return getGenerateResult({
    templateId,
    requestId,
  })
    .then(data => {
      const {
        progressStatus,
        thoughtChain,
        selectItems,
        analyze = {},
        pictureAnalyze,
        contents,
        publishResult,
      } = data;

      return {
        content: thoughtChain,
        finished: progressStatus === 'FINISHED',
        goodsList: selectItems,
        ...analyze,
        images: pictureAnalyze,
        articleContents: contents,
        publishStatus: publishResult?.publishStatus || false,
        articleUrl: publishResult?.url || '',
      };
    })
    .catch(() => {
      return {
        finished: false,
      };
    });
}

// 循环执行异步函数
async function loop(asyncFunc) {
  // eslint-disable-next-line @youzan/koko/no-setinterval
  const intervalId = setInterval(async () => {
    try {
      const result = await asyncFunc();
      if (result && result.finished === true) {
        clearInterval(intervalId);
      }
    } catch (error) {
      console.error('执行异步函数出错:', error);
      clearInterval(intervalId);
    }
  }, 3000);
}

// 模拟商品列表获取异步函数
export function fetchGoodsSelectResult(opt, callback) {
  // let count = 1;
  // loop(() => {
  //   return fetchGoodsSelectApi(count).then((res: any) => {
  //     count += 1;
  //     callback(res);
  //     return res;
  //   });
  // });

  loop(() => {
    return fetchGenerateResult({
      templateId: FlowTemplateEnum.GoodsSelection,
      requestId: opt.requestId,
    }).then(res => {
      callback(res);
      return res;
    });
  });
}

// 模拟商品卖点获取异步函数
export function fetchGoodsSellPointResult(opt, callback) {
  // let count = 1;
  // loop(() => {
  //   return fetchGoodsSelectApi(count).then((res: any) => {
  //     count += 1;
  //     callback(res);
  //     return res;
  //   });
  // });

  loop(() => {
    return fetchGenerateResult({
      templateId: FlowTemplateEnum.SellingPointAnalysisText,
      requestId: opt.requestId,
    }).then(res => {
      callback(res);
      return res;
    });
  });
}

// 模拟商品卖点获取异步函数
export function fetchGoodsSellPointImagesResult(opt, callback) {
  // let count = 1;
  // loop(() => {
  //   return fetchGoodsSelectApi(count).then((res: any) => {
  //     count += 1;
  //     callback(res);
  //     return res;
  //   });
  // });

  loop(() => {
    return fetchGenerateResult({
      templateId: FlowTemplateEnum.SellingPointAnalysisImage,
      requestId: opt.requestId,
    }).then(res => {
      callback(res);
      return res;
    });
  });
}

// 模拟文章创建异步函数
export function fetchCreateArticleResult(opt, callback) {
  // let count = 1;
  // loop(() => {
  //   return fetchGoodsSelectApi(count).then((res: any) => {
  //     count += 1;
  //     callback(res);
  //     return res;
  //   });
  // });

  loop(() => {
    return fetchGenerateResult({
      templateId: FlowTemplateEnum.ContentGeneration,
      requestId: opt.requestId,
    }).then(res => {
      callback(res);
      return res;
    });
  });
}

// 模拟文章发布异步函数
export function fetchPublishArticleResult(opt, callback) {
  // let count = 1;
  // loop(() => {
  //   fetchGenerateResult({
  //     templateId: FlowTemplateEnum.GoodsSelection,
  //     requestId: opt.requestId,
  //   });
  //   return fetchGoodsSelectApi(count).then((res: any) => {
  //     count += 1;
  //     callback(res);
  //     return res;
  //   });
  // });
  loop(() => {
    return fetchGenerateResult({
      templateId: FlowTemplateEnum.ContentPublication,
      requestId: opt.requestId,
    }).then(res => {
      callback(res);
      return res;
    });
  });
}
