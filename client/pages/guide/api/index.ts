import ajax from 'zan-pc-ajax';

const prefix = '/v4/jiawo/api/guide';

export const createAgent = async (objective: string) => {
  return ajax(`${prefix}/createAgent`, { data: { objective }, method: 'post' });
};

export const getAgentList = async () => {
  return ajax(`${prefix}/getAgentList`);
};

export const defaultGetToken = () =>
  ajax('/v4/goods/materials/getFileUploadToken', { method: 'get' });

export function upload(token: string, file: Blob) {
  const formData = new FormData();
  formData.append('token', token);
  formData.append('file', file);

  return ajax('https://up.qbox.me', {
    method: 'post',
    contentType: 'application/json',
    data: formData,
  });
}

export function asyncGenerate(postData) {
  return ajax(`${prefix}/asyncGenerate`, {
    data: postData,
    method: 'post',
  });
}

export function getGenerateResult(query) {
  return ajax(`${prefix}/getGenerateResult`, {
    data: query,
    method: 'get',
  });
}
