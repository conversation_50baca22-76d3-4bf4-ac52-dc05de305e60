import React, { useEffect, useMemo } from 'react';
import { Button } from 'zent';
import { hot } from 'react-hot-loader/root';
import queryString from 'query-string';

import MessageBoard from './MessageBoard';
import HandleBoard from './HandleBoard';

import useCustomReducer from './store';
import messageReducer from './store/message';
import { FlowStatusEnum, FlowTemplateEnum } from './constants';
import { asyncGenerate } from './api';

const App = () => {
  const {
    getData,
    setIsStart,
    setRequestId,
    setGoods,
    setFlowStatus,
    setShowHandler,
  } = useCustomReducer();
  const { showMessage, initGoodsSelectMessage } = messageReducer();
  const { showHandler, isStart } = getData();
  const [loading, setLoading] = React.useState(false);

  const startRun = () => {
    setLoading(true);
    asyncGenerate({
      templateId: FlowTemplateEnum.GoodsSelection,
      page: 1,
    })
      .then(data => {
        setIsStart(true);
        showMessage(FlowStatusEnum.GoodsSelect, data.requestId);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    const query = queryString.parse(window.location.search);
    if (query.item_id) {
      initGoodsSelectMessage({
        title: query.title,
      });
      setIsStart(true);
      setTimeout(() => {
        // 执行后端接口
        asyncGenerate({
          templateId: FlowTemplateEnum.SellingPointAnalysisText,
          itemId: query.item_id,
        }).then(data => {
          setRequestId(data.requestId);
          showMessage(FlowStatusEnum.GoodsSellPoint, data.requestId);
        });
        setGoods({
          itemId: query.item_id,
          title: query.title,
        });
        setFlowStatus(FlowStatusEnum.GoodsSellPoint);
        setShowHandler(false);
      }, 1500);
    }
  }, []);

  const Content = useMemo(() => {
    if (!isStart) {
      return null;
    }
    return (
      <>
        <div className="message-board-wrap">
          <MessageBoard />
        </div>
        {showHandler && (
          <div className="handle-board-wrap">
            <HandleBoard />
          </div>
        )}
      </>
    );
  }, [isStart, showHandler]);

  return (
    <div className="contianer">
      {!isStart && (
        <div className="start-board-wrap">
          <div className="start-board">
            <div className="start-board-title">
              <h1>微信小店商品内容托管智能体</h1>
            </div>
            <div className="start-board-btn">
              <Button type="primary" onClick={startRun} loading={loading}>
                点此开始运行
              </Button>
            </div>
          </div>
        </div>
      )}
      {Content}
    </div>
  );
};

export default hot(App);
