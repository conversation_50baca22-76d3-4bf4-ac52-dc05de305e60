#app-header, #shared-sidebar, #app-footer {
  display: none !important;
}
body.theme.theme-new-ui div#app-container.container {
  margin: 0 !important;
  padding: 0;
}


.contianer {
  display: flex;
  height: calc(100vh - 76px);
  overflow: hidden;

  .message-board-wrap {
    flex: 1;
    padding: 12px;
    box-shadow: 2px 0 2px 0 rgba(0, 0, 0, 0.04);
  }
  @keyframes widthGrow {
    from { width: 0; }
    to { width: 50%; }
  }

  .handle-board-wrap {
    padding: 12px;
    // display: none;
    animation: widthGrow 0.5s ease-in-out forwards;

  }

  // 启动页面样式
  .start-board-wrap {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;

    // 添加背景装饰
    &::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
      background-size: 50px 50px;
      animation: float 20s ease-in-out infinite;
    }

    .start-board {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border-radius: 20px;
      padding: 60px 80px;
      text-align: center;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      position: relative;
      z-index: 1;
      transform: translateY(0);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
      }

      .start-board-title {
        margin-bottom: 40px;

        h1 {
          font-size: 32px;
          font-weight: 700;
          color: #2c3e50;
          margin: 0;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          line-height: 1.2;
        }
      }

      .start-board-btn {
        :global(.zent-btn) {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border: none;
          border-radius: 50px;
          padding: 16px 40px;
          font-size: 18px;
          font-weight: 600;
          color: white;
          box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
          }

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 25px rgba(102, 126, 234, 0.4);

            &::before {
              left: 100%;
            }
          }

          &:active {
            transform: translateY(0);
          }
        }
      }
    }
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-20px) rotate(180deg);
    }
  }
}

.chat-flow-container {
  min-height: 100px;
  background-image: url('https://img01.yzcdn.cn/upload_files/2025/03/17/FuyY93kIaOCIoQk_bCaXU01PNpai.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding: 4px;
  position: relative;
  box-sizing: border-box;

  div {
    background-color: unset !important;
  }

  .robot-avatar {
    position: absolute;
    left: 0px;
    top: -12px;
  }

  .text-block {
    border-top: 1px solid #eee;
    padding-top: 16px;
    margin: 0 16px 16px 16px;
    height: calc(100vh - 120px);
    overflow: auto;
  }
  
  ul li {
    list-style: disc;
  }
  .message-block-wrap {
    border-bottom: 1px dashed #ddd;
    &:last-child {
      border-bottom: none;
    }
  }
}