import React, { useEffect, useRef } from 'react';

import MessageBlock from './components/MessageBlock';
import messageReducer from './store/message';
import { MessageScrollEvent } from './utils/event';

const MessageBoard = () => {
  const messageWrapFef = useRef(null);
  const { getData } = messageReducer();

  const {
    goodsSelectBlock,
    goodsSellPointBlock,
    goodsSellPointImageBlock,
    createArticleBlock,
    publishArticleBlock,
  } = getData();

  const messageList = [
    goodsSelectBlock,
    goodsSellPointBlock,
    goodsSellPointImageBlock,
    createArticleBlock,
    publishArticleBlock,
  ].filter(item => item);

  useEffect(() => {
    function createTimer() {
      // eslint-disable-next-line @youzan/koko/no-setinterval
      const timer = setInterval(() => {
        const messageWrap = messageWrapFef.current;
        console.log('messageWrap', messageWrap);
        if (messageWrap) {
          // @ts-ignore
          messageWrap.scrollTop = messageWrap.scrollHeight;
        }
      }, 1000);
      return timer;
    }
    let timer = createTimer();
    const offScrollEvent = MessageScrollEvent.listen('scroll', () => {
      if (timer) {
        clearInterval(timer);
      }
      timer = createTimer();
    });
    const offRemoveScrollEvent = MessageScrollEvent.listen('removeScroll', () => {
      clearInterval(timer);
    });
    return () => {
      clearInterval(timer);
      offScrollEvent();
      offRemoveScrollEvent();
    };
  }, []);

  return (
    <div className="chat-flow-container">
      <div className="robot-avatar">
        <img
          src="https://img01.yzcdn.cn/upload_files/2025/01/18/FsMrkM4hyFdlqVPEbwtJ-y538jis.png"
          alt="机器人头像"
        />
      </div>
      <div className="text-block" ref={messageWrapFef}>
        {messageList.map(
          block =>
            block && (
              <div className="message-block-wrap">
                <MessageBlock
                  key={block.id}
                  block={block}
                  messageId={block.id.toString()}
                  type={block.flowType}
                />
              </div>
            ),
        )}
      </div>
    </div>
  );
};

export default MessageBoard;
