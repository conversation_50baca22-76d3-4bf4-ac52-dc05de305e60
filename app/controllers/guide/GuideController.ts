import { GET, Index, Inject, Metadata, POST, Router } from '@youzan/assets-route-plugin';
import { PCBaseController } from '@youzan/wsc-pc-base';
import AgentBuildService from '../../services/agent/AgentBuildService';
import ContentItemService from '../../services/flow/ContentItemService';

@Router('guide')
class GuideController extends PCBaseController {
  @Inject()
  private readonly AgentBuildService!: AgentBuildService;

  @Inject()
  private readonly ContentItemService!: ContentItemService;

  public async init() {
    super.init();
  }

  private getOperator() {
    const { userId } = this.ctx.getState('userInfo');
    const { nickName } = this.ctx.getLocalSession('userInfo');
    return {
      operatorId: String(userId),
      operatorName: nickName,
    };
  }

  @Index(['index'])
  async getIndexHtml() {
    const { ctx } = this;
    // 只放开微商城单店
    return ctx.render('guide/index.html');
  }

  @Metadata('创建智能体')
  @POST('createAgent')
  async createAgent() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const { objective } = this.ctx.request.body;
    const operator = this.getOperator();
    const result = await this.AgentBuildService.submitBuildTask({
      kdtId,
      objective,
      operator,
    });
    this.ctx.successRes(result);
  }

  @Metadata('获取智能体信息')
  @GET('getAgentList')
  async getAgentList() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const operator = this.getOperator();
    const result = await this.AgentBuildService.getBuildResult({
      kdtId,
      operator,
    });
    this.ctx.successRes(result);
  }

  @Metadata('创建异步流程')
  @POST('asyncGenerate')
  async asyncGenerate() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const requestBody = this.ctx.request.body;
    const result = await this.ContentItemService.asyncGenerate({
      kdtId,
      ...requestBody,
    });
    this.ctx.successRes(result);
  }

  @Metadata('异步流程结果查询')
  @GET('getGenerateResult')
  async getGenerateResult() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const requestQuery = this.ctx.request.query;
    const result = await this.ContentItemService.getGenerateResult({
      kdtId,
      ...requestQuery,
    });
    this.ctx.successRes(result);
  }
}

export = GuideController;
