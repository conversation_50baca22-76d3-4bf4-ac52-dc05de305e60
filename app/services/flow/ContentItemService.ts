/* eslint-disable max-len */
import BaseService from '../base/BaseService';

class ContentItemService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.ebiz.video.channels.flow.api.service.content.ContentItemService';
  }

  async asyncGenerate(request) {
    return this.invoke('asyncGenerate', [request], {
      timeout: 10000, // ajax超时
      headers: {
        'X-Timeout': 10000, // tether超时
      },
    });
  }

  async getGenerateResult(request) {
    return this.invoke('getGenerateResult', [request], {
      timeout: 10000, // ajax超时
      headers: {
        'X-Timeout': 10000, // tether超时
      },
    });
  }
}

export default ContentItemService;
